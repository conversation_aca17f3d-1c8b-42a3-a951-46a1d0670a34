// In: com.mea.datasync.test
package com.mea.datasync.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.test.BTestNg;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.persistence.ProfileManager;

/**
 * BProfileManagerTest tests the ProfileManager functionality for
 * saving and loading connection profiles to/from JSON files.
 *
 * This test follows Niagara TestNG conventions:
 * - Extends BTestNg
 * - Uses @Test annotations with groups
 * - Proper setup/teardown methods
 * - TestNG Assert methods
 */
@NiagaraType
@Test(groups = { "datasync", "unit" })
public class BProfileManagerTest extends BTestNg {

//region /*+ ------------ <PERSON>EGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.test.BProfileManagerTest(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BProfileManagerTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Setup and Teardown
////////////////////////////////////////////////////////////////

  private ProfileManager profileManager;
  private java.util.List<String> testProfileNames;

  /**
   * Setup method called once before all test methods in this class.
   * Initializes the ProfileManager and prepares test environment.
   */
  @BeforeClass(alwaysRun = true)
  public void setupBeforeClass() throws Exception {
    System.out.println("=== BProfileManagerTest Setup ===");

    // Initialize ProfileManager
    profileManager = new ProfileManager();
    Assert.assertNotNull(profileManager, "ProfileManager should be created successfully");

    // Initialize list to track test profiles for cleanup
    testProfileNames = new java.util.ArrayList<>();

    // Verify profiles directory exists
    java.io.File profilesDir = profileManager.getProfilesDirectory();
    Assert.assertNotNull(profilesDir, "Profiles directory should not be null");
    Assert.assertTrue(profilesDir.exists(), "Profiles directory should exist");

    System.out.println("ProfileManager initialized successfully");
    System.out.println("Profiles directory: " + profilesDir.getAbsolutePath());
  }

  /**
   * Teardown method called once after all test methods in this class.
   * Cleans up any test profiles created during testing.
   */
  @AfterClass(alwaysRun = true)
  public void teardownAfterClass() throws Exception {
    System.out.println("=== BProfileManagerTest Cleanup ===");

    if (profileManager != null && testProfileNames != null) {
      // Clean up any test profiles that might still exist
      for (String profileName : testProfileNames) {
        try {
          if (profileManager.profileExists(profileName)) {
            boolean deleted = profileManager.deleteProfile(profileName);
            System.out.println("Cleanup profile '" + profileName + "': " +
                             (deleted ? "SUCCESS" : "FAILED"));
          }
        } catch (Exception e) {
          System.err.println("Error cleaning up profile '" + profileName + "': " + e.getMessage());
        }
      }
    }

    System.out.println("BProfileManagerTest cleanup completed");
  }

////////////////////////////////////////////////////////////////
// Test Methods
////////////////////////////////////////////////////////////////

  /**
   * Test basic ProfileManager functionality - creating, saving, and loading profiles.
   * This test follows the given-when-then pattern for clarity.
   */
  @Test(description = "Test ProfileManager save and load functionality",
        groups = { "datasync", "unit", "crud" })
  public void testProfileManagerSaveLoad() {
    System.out.println("Starting ProfileManager save/load test");

    // Given - a test profile with comprehensive data
    String profileName = "TestProfile_SaveLoad";
    testProfileNames.add(profileName); // Track for cleanup

    BConnectionProfile testProfile = new BConnectionProfile();
    testProfile.setSourceType("Excel");
    testProfile.setSourcePath("C:\\Test\\test.xlsx");
    testProfile.setSheetName("TestSheet");
    testProfile.setTargetHost("localhost");
    testProfile.setTargetPath("station:|slot:/Test");
    testProfile.setStatus("Test");
    testProfile.setComponentsCreated(42);

    // Verify properties are set correctly (given state)
    Assert.assertEquals(testProfile.getSourceType(), "Excel", "Source type should be set");
    Assert.assertEquals(testProfile.getSourcePath(), "C:\\Test\\test.xlsx", "Source path should be set");
    Assert.assertEquals(testProfile.getSheetName(), "TestSheet", "Sheet name should be set");
    Assert.assertEquals(testProfile.getTargetHost(), "localhost", "Target host should be set");
    Assert.assertEquals(testProfile.getTargetPath(), "station:|slot:/Test", "Target path should be set");
    Assert.assertEquals(testProfile.getStatus(), "Test", "Status should be set");
    Assert.assertEquals(testProfile.getComponentsCreated(), 42, "Components created should be set");

    // When - save the profile
    boolean saveResult = profileManager.saveProfile(testProfile, profileName);

    // Then - verify save was successful
    Assert.assertTrue(saveResult, "Profile should be saved successfully");

    // And - verify profile exists
    boolean exists = profileManager.profileExists(profileName);
    Assert.assertTrue(exists, "Profile should exist after saving");

    // When - load the profile back
    BConnectionProfile loadedProfile = profileManager.loadProfile(profileName);

    // Then - verify loaded profile is valid and has correct data
    Assert.assertNotNull(loadedProfile, "Loaded profile should not be null");
    Assert.assertEquals(loadedProfile.getSourceType(), "Excel", "Loaded source type should match");
    Assert.assertEquals(loadedProfile.getSourcePath(), "C:\\Test\\test.xlsx", "Loaded source path should match");
    Assert.assertEquals(loadedProfile.getSheetName(), "TestSheet", "Loaded sheet name should match");
    Assert.assertEquals(loadedProfile.getTargetHost(), "localhost", "Loaded target host should match");
    Assert.assertEquals(loadedProfile.getTargetPath(), "station:|slot:/Test", "Loaded target path should match");
    Assert.assertEquals(loadedProfile.getStatus(), "Test", "Loaded status should match");
    Assert.assertEquals(loadedProfile.getComponentsCreated(), 42, "Loaded components created should match");

    // When - delete the test profile
    boolean deleteResult = profileManager.deleteProfile(profileName);

    // Then - verify deletion was successful
    Assert.assertTrue(deleteResult, "Profile should be deleted successfully");

    // And - verify profile no longer exists
    boolean existsAfterDelete = profileManager.profileExists(profileName);
    Assert.assertFalse(existsAfterDelete, "Profile should not exist after deletion");

    System.out.println("ProfileManager save/load test completed successfully");
  }

  /**
   * Test ProfileManager directory creation and file listing.
   * Verifies that the ProfileManager properly manages its file system operations.
   */
  @Test(description = "Test ProfileManager directory and file operations",
        groups = { "datasync", "unit", "filesystem" })
  public void testProfileManagerDirectoryOperations() {
    System.out.println("Starting ProfileManager directory operations test");

    // Given - ProfileManager is initialized (from setup)
    // When - get profiles directory
    java.io.File profilesDir = profileManager.getProfilesDirectory();

    // Then - verify directory properties
    Assert.assertNotNull(profilesDir, "Profiles directory should not be null");
    Assert.assertTrue(profilesDir.exists(), "Profiles directory should exist");
    Assert.assertTrue(profilesDir.isDirectory(), "Profiles directory should be a directory");

    // When - list existing profiles
    java.util.List<String> profiles = profileManager.listProfiles();

    // Then - verify list is valid
    Assert.assertNotNull(profiles, "Profile list should not be null");

    System.out.println("Found " + profiles.size() + " existing profiles");
    for (String profile : profiles) {
      System.out.println("  - " + profile);
    }

    System.out.println("ProfileManager directory operations test completed successfully");
  }

  /**
   * Test enhanced JSON structure with nested objects.
   * Verifies that the new nested JSON schema is properly serialized and deserialized.
   */
  @Test(description = "Test enhanced JSON structure with nested objects",
        groups = { "datasync", "unit", "json", "schema" })
  public void testEnhancedJsonStructure() {
    System.out.println("Starting enhanced JSON structure test");

    // Given - a comprehensive test profile with all properties
    String profileName = "EnhancedStructureTest";
    testProfileNames.add(profileName); // Track for cleanup

    BConnectionProfile testProfile = new BConnectionProfile();
    testProfile.setSourceType("Excel");
    testProfile.setSourcePath("C:\\Data\\comprehensive_test.xlsx");
    testProfile.setSheetName("BACnet_Points");
    testProfile.setTargetHost("*************");
    testProfile.setTargetUsername("admin");
    testProfile.setTargetPath("station:|slot:/Drivers/BACnet");
    testProfile.setStatus("Ready");
    testProfile.setComponentsCreated(150);
    testProfile.setLastError("");

    // When - save the profile (should use enhanced JSON structure)
    boolean saveResult = profileManager.saveProfile(testProfile, profileName);

    // Then - verify save was successful
    Assert.assertTrue(saveResult, "Enhanced profile should be saved successfully");

    // When - load the profile back
    BConnectionProfile loadedProfile = profileManager.loadProfile(profileName);

    // Then - verify loaded profile is valid
    Assert.assertNotNull(loadedProfile, "Loaded enhanced profile should not be null");

      // Verify all data integrity
      Assert.assertEquals(loadedProfile.getSourceType(), "Excel", "Source type should match");
      Assert.assertEquals(loadedProfile.getSourcePath(), "C:\\Data\\comprehensive_test.xlsx", "Source path should match");
      Assert.assertEquals(loadedProfile.getSheetName(), "BACnet_Points", "Sheet name should match");
      Assert.assertEquals(loadedProfile.getTargetHost(), "*************", "Target host should match");
      Assert.assertEquals(loadedProfile.getTargetUsername(), "admin", "Target username should match");
      Assert.assertEquals(loadedProfile.getTargetPath(), "station:|slot:/Drivers/BACnet", "Target path should match");
      Assert.assertEquals(loadedProfile.getStatus(), "Ready", "Status should match");
      Assert.assertEquals(loadedProfile.getComponentsCreated(), 150, "Components created should match");
      Assert.assertEquals(loadedProfile.getLastError(), "", "Last error should match");

      // Verify JSON file structure by reading raw file
      java.io.File profileFile = new java.io.File(profileManager.getProfilesDirectory(),
                                                  "EnhancedStructureTest.json");
      Assert.assertTrue(profileFile.exists(), "JSON file should exist");

      // Read and verify JSON contains nested structure
      String jsonContent = new String(java.nio.file.Files.readAllBytes(profileFile.toPath()));
      Assert.assertTrue(jsonContent.contains("sourceConfig"), "JSON should contain sourceConfig object");
      Assert.assertTrue(jsonContent.contains("targetNiagaraStation"), "JSON should contain targetNiagaraStation object");
      Assert.assertTrue(jsonContent.contains("syncMetadata"), "JSON should contain syncMetadata object");
      Assert.assertTrue(jsonContent.contains("schemaVersion"), "JSON should contain schemaVersion");

      System.out.println("JSON structure verification:");
      System.out.println("  Contains sourceConfig: " + jsonContent.contains("sourceConfig"));
      System.out.println("  Contains targetNiagaraStation: " + jsonContent.contains("targetNiagaraStation"));
      System.out.println("  Contains syncMetadata: " + jsonContent.contains("syncMetadata"));

      // Clean up
      boolean deleteResult = profileManager.deleteProfile(profileName);
      Assert.assertTrue(deleteResult, "Enhanced profile should be deleted successfully");

      System.out.println("Enhanced JSON structure test completed successfully");

    } catch (Exception e) {
      System.err.println("Enhanced JSON test failed with exception: " + e.getMessage());
      e.printStackTrace();
      Assert.fail("Enhanced JSON test failed with exception: " + e.getMessage());
    }
  }

  /**
   * Test profile validation and error handling.
   * Verifies that ProfileManager handles edge cases and invalid inputs gracefully.
   */
  @Test(description = "Test profile validation and error handling",
        groups = { "datasync", "unit", "validation", "error-handling" })
  public void testProfileValidationAndErrorHandling() {
    System.out.println("Starting profile validation and error handling test");

    // Given - a test profile for validation tests
    BConnectionProfile testProfile = new BConnectionProfile();
    testProfile.setSourceType("Excel");
    testProfile.setSourcePath("C:\\Test\\validation.xlsx");

    // When/Then - test edge cases for profile names

    // Test with empty profile name
    boolean result1 = profileManager.saveProfile(testProfile, "");
    System.out.println("Save with empty name result: " + result1);

    // Test with null profile name
    boolean result2 = profileManager.saveProfile(testProfile, null);
    System.out.println("Save with null name result: " + result2);

    // Test loading non-existent profile
    BConnectionProfile nonExistent = profileManager.loadProfile("NonExistentProfile");
    Assert.assertNull(nonExistent, "Loading non-existent profile should return null");

    // Test profile exists for non-existent profile
    boolean exists = profileManager.profileExists("NonExistentProfile");
    Assert.assertFalse(exists, "Non-existent profile should not exist");

    // Test deleting non-existent profile
    boolean deleteResult = profileManager.deleteProfile("NonExistentProfile");
    System.out.println("Delete non-existent profile result: " + deleteResult);

    // Test with special characters in profile name
    String specialName = "Test Profile with Spaces & Special-Chars_123";
    testProfileNames.add(specialName); // Track for cleanup

    boolean saveSpecial = profileManager.saveProfile(testProfile, specialName);
    Assert.assertTrue(saveSpecial, "Profile with special characters should save");

    // Verify it can be loaded back
    BConnectionProfile loadedSpecial = profileManager.loadProfile(specialName);
    Assert.assertNotNull(loadedSpecial, "Profile with special characters should load");

    // Clean up immediately (also tracked in testProfileNames for safety)
    boolean cleanupResult = profileManager.deleteProfile(specialName);
    Assert.assertTrue(cleanupResult, "Special character profile should be deleted");

    System.out.println("Profile validation and error handling test completed successfully");
  }
}

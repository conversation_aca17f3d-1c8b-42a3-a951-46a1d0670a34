@echo off
echo === N4-DataSync Certificate Fix ===
echo.

REM Navigate to security directory
cd /d "C:\Users\<USER>\IdeaProjects\datasync\security"

echo Current directory: %CD%
echo.

echo Step 1: Creating a new self-signed certificate with proper chain...
echo.

REM Create a new keystore with proper certificate chain
keytool -genkeypair ^
    -alias myN4DataSyncCert ^
    -keyalg RSA ^
    -keysize 3072 ^
    -validity 365 ^
    -keystore niagara-signing-new.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -keypass hf24ghn312dc5pqjdrf ^
    -dname "CN=N4-DataSync Development Certificate, OU=Development, O=Tridium, L=Richmond, ST=Virginia, C=US" ^
    -ext KeyUsage=digitalSignature,keyEncipherment ^
    -ext ExtendedKeyUsage=codeSigning

if %ERRORLEVEL% neq 0 (
    echo Error creating keystore!
    pause
    exit /b 1
)

echo.
echo Step 2: Exporting certificate for import into Niagara...
echo.

REM Export the certificate
keytool -exportcert ^
    -alias myN4DataSyncCert ^
    -keystore niagara-signing-new.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -file myN4DataSyncCert-new.cer ^
    -rfc

if %ERRORLEVEL% neq 0 (
    echo Error exporting certificate!
    pause
    exit /b 1
)

echo.
echo Step 3: Creating PEM format for easy import...
echo.

REM Convert to PEM format (same as .cer but with .pem extension)
copy myN4DataSyncCert-new.cer myN4DataSyncCert-new.pem

echo.
echo === Certificate Creation Complete ===
echo.
echo Files created:
echo   - niagara-signing-new.jks (keystore for signing)
echo   - myN4DataSyncCert-new.cer (certificate for import)
echo   - myN4DataSyncCert-new.pem (certificate in PEM format)
echo.
echo Next steps:
echo 1. Update your signing configuration to use niagara-signing-new.jks
echo 2. Import myN4DataSyncCert-new.pem into Niagara Workbench
echo 3. Rebuild and redeploy your module
echo.
pause

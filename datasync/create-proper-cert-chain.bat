@echo off
echo === Creating Proper Certificate Chain for N4-DataSync ===
echo.

REM Navigate to security directory
cd /d "C:\Users\<USER>\IdeaProjects\datasync\security"

echo Current directory: %CD%
echo.

echo Step 1: Creating Root CA Certificate...
echo.

REM Create Root CA private key and certificate
keytool -genkeypair ^
    -alias rootCA ^
    -keyalg RSA ^
    -keysize 3072 ^
    -validity 3650 ^
    -keystore rootCA.jks ^
    -storepass rootpass123 ^
    -keypass rootpass123 ^
    -dname "CN=N4-DataSync Root CA, OU=Development CA, O=Tridium Development, L=Richmond, ST=Virginia, C=US" ^
    -ext BasicConstraints:critical=ca:true ^
    -ext KeyUsage:critical=keyCertSign,cRLSign

if %ERRORLEVEL% neq 0 (
    echo Error creating Root CA!
    pause
    exit /b 1
)

echo.
echo Step 2: Exporting Root CA certificate...
echo.

REM Export Root CA certificate
keytool -exportcert ^
    -alias rootCA ^
    -keystore rootCA.jks ^
    -storepass rootpass123 ^
    -file rootCA.cer ^
    -rfc

echo.
echo Step 3: Creating Module Signing Certificate signed by Root CA...
echo.

REM Create module signing certificate
keytool -genkeypair ^
    -alias myN4DataSyncCert ^
    -keyalg RSA ^
    -keysize 3072 ^
    -validity 365 ^
    -keystore module-signing.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -keypass hf24ghn312dc5pqjdrf ^
    -dname "CN=N4-DataSync Module Certificate, OU=Development, O=Tridium Development, L=Richmond, ST=Virginia, C=US" ^
    -ext KeyUsage:critical=digitalSignature ^
    -ext ExtendedKeyUsage=codeSigning

echo.
echo Step 4: Creating Certificate Signing Request...
echo.

REM Create CSR for module certificate
keytool -certreq ^
    -alias myN4DataSyncCert ^
    -keystore module-signing.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -file module.csr

echo.
echo Step 5: Signing module certificate with Root CA...
echo.

REM Sign the module certificate with Root CA
keytool -gencert ^
    -alias rootCA ^
    -keystore rootCA.jks ^
    -storepass rootpass123 ^
    -infile module.csr ^
    -outfile module-signed.cer ^
    -validity 365 ^
    -ext KeyUsage:critical=digitalSignature ^
    -ext ExtendedKeyUsage=codeSigning ^
    -rfc

echo.
echo Step 6: Importing certificates into module keystore...
echo.

REM Import Root CA into module keystore
keytool -importcert ^
    -alias rootCA ^
    -keystore module-signing.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -file rootCA.cer ^
    -noprompt

REM Import signed module certificate
keytool -importcert ^
    -alias myN4DataSyncCert ^
    -keystore module-signing.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -file module-signed.cer ^
    -noprompt

echo.
echo Step 7: Creating certificate chain file for Niagara import...
echo.

REM Create certificate chain file (Root CA + Module cert)
copy rootCA.cer + module-signed.cer cert-chain.pem

echo.
echo Step 8: Exporting final certificate chain...
echo.

REM Export the complete certificate chain
keytool -exportcert ^
    -alias myN4DataSyncCert ^
    -keystore module-signing.jks ^
    -storepass 1qvg4pu8gq5b7hua29k7 ^
    -file myN4DataSyncCert-chain.pem ^
    -rfc

echo.
echo === Certificate Chain Creation Complete ===
echo.
echo Files created:
echo   - rootCA.jks (Root CA keystore)
echo   - rootCA.cer (Root CA certificate - IMPORT THIS INTO NIAGARA)
echo   - module-signing.jks (Module signing keystore - USE FOR SIGNING)
echo   - myN4DataSyncCert-chain.pem (Complete certificate chain)
echo   - cert-chain.pem (Certificate chain for import)
echo.
echo IMPORTANT NEXT STEPS:
echo 1. Import rootCA.cer into Niagara Workbench as a trusted CA
echo 2. Update signing configuration to use module-signing.jks
echo 3. Rebuild your module
echo 4. The certificate chain should now validate properly
echo.
pause

@echo off
echo === N4-DataSync JSON Structure Test ===
echo.

REM Set up classpath with Gson library
set GSON_JAR=datasync-wb\build\libs\gson-2.10.1.jar
set CLASSPATH=.;%GSON_JAR%

REM Check if Gson jar exists in the build output
if not exist "%GSON_JAR%" (
    echo Looking for Gson in Gradle cache...
    for /r "%USERPROFILE%\.gradle\caches" %%f in (gson-2.10.1.jar) do (
        if exist "%%f" (
            set GSON_JAR=%%f
            goto :found_gson
        )
    )
    echo Warning: Gson JAR not found, test may fail
    :found_gson
)

echo Using Gson JAR: %GSON_JAR%
echo.

REM Compile and run the test
javac -cp "%GSON_JAR%" test-runner.java
if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

java -cp ".;%GSON_JAR%" TestRunner

echo.
echo Test completed. Check the output above for results.
pause
